{"$schema": "https://turborepo.com/schema.json", "globalDependencies": [".env*"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "dev:client": {"cache": false, "persistent": true}, "clean": {"cache": false}, "db:push": {"cache": false}, "db:generate": {"cache": false}, "db:studio": {"cache": false}}, "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_*"]}