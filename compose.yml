services:
 
  client:
    build: 
      context: .
      dockerfile: client.Dockerfile
    ports:
      - "3001:3000"
    restart: unless-stopped
    environment:
      - NODE_ENV=PRODUCTION
      - DATABASE_URL="postgresql://postgres:1234@localhost:5432/next_starter?schema=public"
  admin:
    build: 
      context: .
      dockerfile: admin.Dockerfile
    ports:
      - "3000:3000"
    restart: unless-stopped
    environment:
      - NODE_ENV=PRODUCTION
      - DATABASE_URL="postgresql://postgres:1234@localhost:5432/next_starter?schema=public"
