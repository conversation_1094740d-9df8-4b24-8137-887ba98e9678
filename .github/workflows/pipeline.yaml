name: pipeline
on:
  push:
    branches:
      - "main"
permissions:
  contents: read  
  packages: write
jobs:
  run-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22"
      - name: Enable Corepack and install pnpm
        run: |
          corepack enable
          corepack prepare pnpm@9.0.0 --activate
      - name: Install dependencies
        run: pnpm install
      - name: Build App
        run: pnpm run build
  build-and-push-image:
    runs-on: ubuntu-latest
    needs:
      - run-tests
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build and push web image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: admin.Dockerfile
          push: true
          tags: |
            ghcr.io/shemaikuzwe/next-turborepo-starter/next-web:latest
            ghcr.io/shemaikuzwe/next-turborepo-starter/next-web:${{ github.sha }}
      - name: Build and push docs image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: client.Dockerfile
          push: true
          tags: |
            ghcr.io/shemaikuzwe/next-turborepo-starter/next-docs:latest
            ghcr.io/shemaikuzwe/next-turborepo-starter/next-docs:${{ github.sha }}
  # deploy:
  #   runs-on: ubuntu-latest
  #   needs:
  #     - build-and-push-image
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v2

  #   - name: create env file
  #     run: |
  #       echo "GIT_COMMIT_HASH=${{ github.sha }}" >> ./envfile

  #   - name: Docker Stack Deploy
  #     uses: cssnr/stack-deploy-action@v1
  #     with:
  #       name: next-turborepo-starter
  #       file: docker-stack.yaml
  #       host: ${{ secrets.HOST }}
  #       user: deploy
  #       ssh_key: ${{ secrets.DEPLOY_SSH_PRIVATE_KEY }}
  #       env_file: ./envfile          