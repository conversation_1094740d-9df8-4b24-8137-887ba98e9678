generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


model Product{
  id  String  @id @default(uuid())  @db.Uuid
  name   String @unique
  description String?
  isPopular Boolean @default(false)
  features String[] @default([])
  price  Int 
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

}