{"extends": "@next-starter/typescript-config/react-library.json", "compilerOptions": {"baseUrl": ".", "paths": {"@next-starter/ui/*": ["./src/*"]}}, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "skipLibCheck": true, "isolatedModules": true, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"]}